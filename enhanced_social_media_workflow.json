{"name": "GOD Digital Marketing - Ultimate Social Media Automation System", "nodes": [{"parameters": {"rule": {"interval": [{"field": "cronExpression", "expression": "0 6 * * *"}]}}, "id": "daily-scheduler-001", "name": "Daily Content Scheduler", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [-2400, 100]}, {"parameters": {}, "id": "manual-trigger-001", "name": "Manual Test Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-2400, 200]}, {"parameters": {"jsCode": "// GOD Digital Marketing - Ultimate AI Configuration Engine\nconst currentTime = new Date();\nconst currentDay = currentTime.getDay();\nconst currentHour = currentTime.getHours();\nconst rotationDay = currentDay === 0 ? 7 : currentDay;\n\n// Ultimate Configuration for Complete Social Media Automation\nconst godDigitalConfig = {\n  // Company Identity & Branding\n  company: {\n    name: 'GOD Digital Marketing',\n    website: 'https://godigitalmarketing.com',\n    tagline: 'Transforming Businesses Through AI-Powered Digital Solutions',\n    value_proposition: 'We Generate 500%+ ROI Through AI-Powered Digital Marketing, Automation & Development Solutions',\n    mission: 'To democratize advanced digital marketing through AI automation',\n    vision: 'Becoming the global leader in AI-powered marketing transformation',\n    phone: '******-GOD-DIGITAL',\n    email: '<EMAIL>',\n    social_handles: {\n      facebook: '@godigitalmarketing',\n      instagram: '@godigitalmarketing',\n      linkedin: 'god-digital-marketing',\n      twitter: '@goddigitalmark',\n      youtube: '@godigitalmarketing',\n      tiktok: '@godigitalmarketing'\n    }\n  },\n  \n  // Comprehensive Service Portfolio\n  services: {\n    digital_marketing: [\n      'AI-Powered SEO & Content Optimization ($500-2000/month)',\n      'Automated PPC Campaign Management ($300-1500/month)',\n      'Social Media Marketing Automation ($200-800/month)',\n      'Email Marketing & Lead Nurturing ($150-600/month)',\n      'Conversion Rate Optimization ($400-1200/month)',\n      'Advanced Analytics & Reporting ($200-500/month)',\n      'Influencer Marketing & Partnerships ($300-1000/month)',\n      'Brand Strategy & Digital Positioning ($500-1500/month)'\n    ],\n    ai_automation: [\n      'n8n Workflow Development ($800-3000/project)',\n      'ChatGPT Business Integration ($600-2500/project)',\n      'Customer Service Automation ($500-2000/project)',\n      'Lead Generation Automation ($400-1500/project)',\n      'Content Creation Automation ($300-1000/project)',\n      'Business Process Optimization ($700-2500/project)',\n      'AI Chatbot Development ($800-3500/project)',\n      'Predictive Analytics Setup ($1000-4000/project)'\n    ],\n    development: [\n      'Custom Web Development ($1500-8000/project)',\n      'E-commerce Solutions ($2000-10000/project)',\n      'Mobile App Development ($3000-15000/project)',\n      'API Development & Integration ($800-4000/project)',\n      'Database Design & Optimization ($600-3000/project)',\n      'Cloud Infrastructure Setup ($500-2500/project)',\n      'Progressive Web Apps ($2000-8000/project)',\n      'DevOps & CI/CD Implementation ($1000-5000/project)'\n    ]\n  },\n  \n  // Revolutionary 7-Day Content Strategy Framework\n  content_strategy: {\n    rotation_cycle: 7,\n    current_day: rotationDay,\n    current_hour: currentHour,\n    strategies: {\n      1: { // Monday - \"Motivation Monday\"\n        type: 'motivation',\n        theme: 'Motivation Monday',\n        focus: 'Inspirational business content with custom graphics',\n        goal: 'Inspire and motivate audience to take action',\n        psychology: 'Inspiration, Motivation & Emotional Triggers',\n        content_pillars: [\n          'Inspirational Business Quotes with Custom Graphics',\n          'Success Stories and Case Studies',\n          'Industry Motivation and Mindset Content',\n          'Entrepreneurial Journey Stories',\n          'Achievement Celebrations',\n          'Vision and Goal Setting'\n        ],\n        engagement_tactics: [\n          'Motivational quote graphics',\n          'Success story videos',\n          'Client transformation highlights',\n          'Behind-the-scenes inspiration',\n          'Goal-setting challenges'\n        ],\n        cta_strategies: [\n          'Start your transformation today',\n          'Take the first step toward success',\n          'Join our community of achievers',\n          'Book your free strategy call',\n          'Download our success roadmap'\n        ],\n        hashtags: '#MotivationMonday #BusinessInspiration #EntrepreneurLife #SuccessStories #MarketingMotivation #BusinessTransformation #EntrepreneurJourney #GODDigitalMarketing',\n        optimal_times: ['08:00', '12:00', '17:00'],\n        content_formats: ['quote_graphic', 'video', 'story', 'carousel', 'testimonial']\n      },\n      2: { // Tuesday - \"Tutorial Tuesday\"\n        type: 'tutorial',\n        theme: 'Tutorial Tuesday',\n        focus: 'Educational how-to content and marketing strategies',\n        goal: 'Establish authority through valuable educational content',\n        psychology: 'Authority, Trust & Expertise',\n        content_pillars: [\n          'Educational How-to Content',\n          'Marketing Tips and Strategies',\n          'Tool Tutorials and Best Practices',\n          'Step-by-Step Implementation Guides',\n          'Technical Deep Dives',\n          'Industry Best Practices'\n        ],\n        engagement_tactics: [\n          'Interactive tutorials',\n          'Step-by-step guides',\n          'Tool demonstrations',\n          'Live Q&A sessions',\n          'Educational carousels'\n        ],\n        cta_strategies: [\n          'Save this tutorial for later',\n          'Download the complete guide',\n          'Join our masterclass',\n          'Get personalized training',\n          'Access our resource library'\n        ],\n        hashtags: '#TutorialTuesday #DigitalMarketing #MarketingStrategy #MarketingEducation #MarketingTips #DigitalTransformation #MarketingTutorial #GODDigitalMarketing',\n        optimal_times: ['09:00', '13:00', '18:00'],\n        content_formats: ['carousel', 'video', 'infographic', 'thread', 'guide']\n      },\n      3: { // Wednesday - \"Wisdom Wednesday\"\n        type: 'wisdom',\n        theme: 'Wisdom Wednesday',\n        focus: 'Industry insights, trends, and expert thought leadership',\n        goal: 'Position as industry thought leader and expert',\n        psychology: 'Authority, Innovation & Insider Knowledge',\n        content_pillars: [\n          'Industry Insights and Trends',\n          'Expert Advice and Thought Leadership',\n          'Data-Driven Marketing Facts',\n          'Market Analysis and Predictions',\n          'Strategic Business Insights',\n          'Innovation Spotlights'\n        ],\n        engagement_tactics: [\n          'Industry trend analysis',\n          'Expert predictions',\n          'Data visualizations',\n          'Market research insights',\n          'Thought leadership articles'\n        ],\n        cta_strategies: [\n          'Stay ahead with our insights',\n          'Get the competitive advantage',\n          'Access exclusive research',\n          'Join industry leaders',\n          'Download our trend report'\n        ],\n        hashtags: '#WisdomWednesday #MarketingTrends #DigitalInnovation #IndustryInsights #ThoughtLeadership #MarketingInnovation #FutureOfMarketing #GODDigitalMarketing',\n        optimal_times: ['10:00', '14:00', '19:00'],\n        content_formats: ['infographic', 'carousel', 'video', 'thread', 'article']\n      },\n      4: { // Thursday - \"Throwback Thursday\"\n        type: 'throwback',\n        theme: 'Throwback Thursday',\n        focus: 'Company milestones, achievements, and industry evolution',\n        goal: 'Build credibility through documented success and history',\n        psychology: 'Social Proof, Nostalgia & Trust Building',\n        content_pillars: [\n          'Company Milestones and Achievements',\n          'Client Success Transformations',\n          'Industry Evolution Content',\n          'Historical Marketing Insights',\n          'Journey and Growth Stories',\n          'Before and After Showcases'\n        ],\n        engagement_tactics: [\n          'Timeline showcases',\n          'Transformation stories',\n          'Milestone celebrations',\n          'Historical comparisons',\n          'Evolution narratives'\n        ],\n        cta_strategies: [\n          'Ready for similar results?',\n          'Join our success stories',\n          'See how we can help you',\n          'Book your transformation call',\n          'Become our next success story'\n        ],\n        hashtags: '#ThrowbackThursday #ClientSuccess #BusinessTransformation #MarketingResults #CompanyMilestones #SuccessStories #MarketingWins #GODDigitalMarketing',\n        optimal_times: ['08:30', '13:30', '17:30'],\n        content_formats: ['carousel', 'video', 'infographic', 'story', 'testimonial']\n      },\n      5: { // Friday - \"Feature Friday\"\n        type: 'feature',\n        theme: 'Feature Friday',\n        focus: 'Tool and service showcases, client spotlights, behind-the-scenes',\n        goal: 'Showcase capabilities and build personal connections',\n        psychology: 'Social Proof, Transparency & Trust Building',\n        content_pillars: [\n          'Tool and Service Showcases',\n          'Client Spotlights',\n          'Behind-the-Scenes Content',\n          'Team Introductions',\n          'Process Demonstrations',\n          'Technology Features'\n        ],\n        engagement_tactics: [\n          'Product demonstrations',\n          'Client interviews',\n          'Behind-the-scenes videos',\n          'Team spotlights',\n          'Process walkthroughs'\n        ],\n        cta_strategies: [\n          'Try this tool for free',\n          'Book a demo today',\n          'Meet our team',\n          'See how we work',\n          'Get a behind-the-scenes look'\n        ],\n        hashtags: '#FeatureFriday #ToolShowcase #ClientSpotlight #BehindTheScenes #TeamSpotlight #MarketingTools #ProcessDemo #GODDigitalMarketing',\n        optimal_times: ['11:00', '15:00', '20:00'],\n        content_formats: ['video', 'carousel', 'story', 'demo', 'interview']\n      },\n      6: { // Saturday - \"Social Saturday\"\n        type: 'social',\n        theme: 'Social Saturday',\n        focus: 'Interactive engagement, polls, and community building',\n        goal: 'Maximize engagement and build community connections',\n        psychology: 'Belonging, Community & Social Connection',\n        content_pillars: [\n          'Interactive Polls and Questions',\n          'User-Generated Content Features',\n          'Community Engagement Posts',\n          'Social Challenges',\n          'Collaborative Content',\n          'Weekend Conversations'\n        ],\n        engagement_tactics: [\n          'Interactive polls',\n          'Q&A sessions',\n          'Community challenges',\n          'User-generated content',\n          'Social conversations'\n        ],\n        cta_strategies: [\n          'Join the conversation',\n          'Share your thoughts',\n          'Tag a friend',\n          'Vote in our poll',\n          'Show us your results'\n        ],\n        hashtags: '#SocialSaturday #CommunityEngagement #InteractivePoll #UserGeneratedContent #CommunityFirst #SocialMedia #Engagement #GODDigitalMarketing',\n        optimal_times: ['10:00', '16:00', '21:00'],\n        content_formats: ['poll', 'story', 'ugc', 'live', 'interactive']\n      },\n      7: { // Sunday - \"Success Sunday\"\n        type: 'success',\n        theme: 'Success Sunday',\n        focus: 'Weekly recaps, testimonials, and call-to-action focused content',\n        goal: 'Drive conversions and showcase weekly achievements',\n        psychology: 'Achievement, Social Proof & Action Orientation',\n        content_pillars: [\n          'Weekly Recap and Upcoming Previews',\n          'Testimonials and Reviews',\n          'Call-to-Action Focused Content',\n          'Success Metrics and Results',\n          'Achievement Celebrations',\n          'Next Week Previews'\n        ],\n        engagement_tactics: [\n          'Weekly summaries',\n          'Testimonial highlights',\n          'Success metrics',\n          'Achievement showcases',\n          'Preview teasers'\n        ],\n        cta_strategies: [\n          'Ready to get started?',\n          'Book your free consultation',\n          'Join our success stories',\n          'Don\\'t wait - act now',\n          'Start your transformation Monday'\n        ],\n        hashtags: '#SuccessSunday #WeeklyRecap #Testimonials #CallToAction #SuccessStories #Results #Achievement #GODDigitalMarketing',\n        optimal_times: ['09:00', '15:00', '19:00'],\n        content_formats: ['recap', 'testimonial', 'cta', 'metrics', 'preview']\n      }\n    }\n  },\n  \n  // Advanced AI Configuration Stack\n  ai_config: {\n    text_generation: {\n      primary: 'meta-llama/llama-3.1-70b-versatile',\n      backup: 'meta-llama/mixtral-8x7b-32768',\n      local_fallback: 'ollama',\n      temperature: 0.7,\n      max_tokens: 4000\n    },\n    image_generation: {\n      primary: 'stabilityai/stable-diffusion-xl-base-1.0',\n      secondary: 'leonardo.ai',\n      fallback: 'unsplash_api',\n      enhancement: {\n        upscaling: 'real-esrgan',\n        face_enhancement: 'gfpgan',\n        background_removal: 'remove.bg'\n      }\n    },\n    content_quality_threshold: 8.5,\n    brand_consistency: 'strict',\n    moderation_enabled: true\n  },\n  \n  // Cost Optimization Strategy\n  cost_optimization: {\n    monthly_budget: 75,\n    free_tier_usage: {\n      groq: '14400_requests_daily',\n      huggingface: '1000_calls_monthly',\n      unsplash: '50_downloads_hourly',\n      n8n: 'self_hosted_free'\n    },\n    priority_spending: ['premium_image_apis', 'advanced_analytics'],\n    cost_tracking: true\n  },\n  \n  // Comprehensive Platform Configuration\n  platform_config: {\n    facebook: {\n      optimal_length: '100-300 chars',\n      best_times: ['09:00', '13:00', '15:00'],\n      content_types: ['posts', 'stories', 'reels', 'video'],\n      engagement_focus: 'community_building',\n      api: 'meta_business_api',\n      posting_enabled: true\n    },\n    instagram: {\n      optimal_length: '125-150 chars',\n      best_times: ['11:00', '14:00', '17:00', '20:00'],\n      content_types: ['feed', 'stories', 'reels', 'igtv'],\n      engagement_focus: 'visual_storytelling',\n      api: 'meta_business_api',\n      posting_enabled: true\n    },\n    linkedin: {\n      optimal_length: '150-300 chars',\n      best_times: ['08:00', '12:00', '17:00'],\n      content_types: ['posts', 'articles', 'stories'],\n      engagement_focus: 'professional_networking',\n      api: 'linkedin_api',\n      posting_enabled: true\n    },\n    twitter: {\n      optimal_length: '71-100 chars',\n      best_times: ['09:00', '13:00', '16:00', '19:00'],\n      content_types: ['tweets', 'threads', 'polls'],\n      engagement_focus: 'real_time_engagement',\n      api: 'twitter_api_v2',\n      posting_enabled: true\n    },\n    youtube: {\n      optimal_length: '200+ chars',\n      best_times: ['18:00', '19:00', '20:00'],\n      content_types: ['shorts', 'videos', 'live'],\n      engagement_focus: 'educational_content',\n      api: 'youtube_data_api',\n      posting_enabled: false\n    },\n    tiktok: {\n      optimal_length: '100-150 chars',\n      best_times: ['18:00', '19:00', '21:00'],\n      content_types: ['short_videos', 'trends'],\n      engagement_focus: 'viral_content',\n      api: 'tiktok_business_api',\n      posting_enabled: false\n    },\n    pinterest: {\n      optimal_length: '200+ chars',\n      best_times: ['20:00', '21:00', '22:00'],\n      content_types: ['pins', 'boards'],\n      engagement_focus: 'search_optimization',\n      api: 'pinterest_api',\n      posting_enabled: false\n    },\n    telegram: {\n      optimal_length: '300+ chars',\n      best_times: ['10:00', '15:00', '20:00'],\n      content_types: ['messages', 'media'],\n      engagement_focus: 'direct_communication',\n      api: 'telegram_bot_api',\n      posting_enabled: false\n    }\n  },\n  \n  // Lead Generation Integration\n  lead_generation: {\n    content_funnel: {\n      awareness: ['motivation', 'tutorial'],\n      interest: ['throwback', 'wisdom'],\n      consideration: ['feature', 'social'],\n      decision: ['success']\n    },\n    cta_strategy: {\n      soft: 'engagement_focused',\n      medium: 'resource_download',\n      strong: 'consultation_booking'\n    },\n    capture_mechanisms: [\n      'link_in_bio_optimization',\n      'story_polls_questions',\n      'comment_to_dm_automation',\n      'free_resource_downloads',\n      'webinar_registrations'\n    ]\n  }\n};\n\n// Intelligent Strategy Selection\ngodDigitalConfig.todays_strategy = godDigitalConfig.content_strategy.strategies[rotationDay];\n\n// AI-Powered Optimal Timing\nconst strategy = godDigitalConfig.todays_strategy;\nconst optimalTimes = strategy.optimal_times;\nconst nextOptimalTime = optimalTimes.find(time => {\n  const [hour, minute] = time.split(':').map(Number);\n  return hour > currentHour || (hour === currentHour && minute > currentTime.getMinutes());\n}) || optimalTimes[0];\n\ngodDigitalConfig.optimal_posting_time = nextOptimalTime;\n\nreturn {\n  ...godDigitalConfig,\n  rotation_day: rotationDay,\n  current_hour: currentHour,\n  day_name: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][currentDay],\n  next_optimal_time: nextOptimalTime,\n  config_version: 'ultimate_v4.0',\n  timestamp: currentTime.toISOString(),\n  system_ready: true\n};"}, "id": "ultimate-config-001", "name": "Ultimate AI Configuration", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2200, 150]}, {"parameters": {"model": "meta-llama/llama-3.1-70b-versatile", "options": {"temperature": 0.8, "maxTokens": 4000}}, "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "typeVersion": 1, "position": [-2000, 150], "id": "groq-ai-generator-001", "name": "Groq AI Content Generator", "credentials": {"groqApi": {"id": "groq-credentials-001", "name": "Groq API"}}}, {"parameters": {"promptType": "define", "text": "=You are the world's most advanced AI marketing strategist for GOD Digital Marketing, specializing in client acquisition and conversion psychology. Your mission is to create UNIQUE, platform-specific content that generates leads and converts prospects into paying clients.\n\nTODAY'S STRATEGY: {{ $('Ultimate AI Configuration').item.json.todays_strategy.theme }} - {{ $('Ultimate AI Configuration').item.json.todays_strategy.focus }}\n\nCOMPANY CONTEXT:\n• Agency: GOD Digital Marketing\n• Specialization: AI-Powered Digital Marketing Solutions\n• Target: Small-Medium Businesses & Entrepreneurs\n• Value Prop: 500%+ ROI Through Advanced Automation\n• Website: https://godigitalmarketing.com\n• Services: {{ JSON.stringify($('Ultimate AI Configuration').item.json.services) }}\n\nPSYCHOLOGICAL FRAMEWORK:\n• Primary Psychology: {{ $('Ultimate AI Configuration').item.json.todays_strategy.psychology }}\n• Engagement Tactics: {{ $('Ultimate AI Configuration').item.json.todays_strategy.engagement_tactics }}\n• CTA Strategies: {{ $('Ultimate AI Configuration').item.json.todays_strategy.cta_strategies }}\n• Content Pillars: {{ $('Ultimate AI Configuration').item.json.todays_strategy.content_pillars }}\n\nCLIENT ACQUISITION FOCUS:\n• Pain Points: Manual marketing, low ROI, time-consuming processes\n• Desires: Automated systems, proven results, competitive advantage\n• Objections: Cost concerns, complexity fears, trust issues\n• Solutions: Free consultations, case studies, step-by-step guidance\n\nCREATE UNIQUE CONTENT FOR EACH PLATFORM (JSON FORMAT):\n{\n  \"facebook_post\": \"Community-focused post with social proof and clear CTA\",\n  \"facebook_story\": \"Behind-the-scenes story with swipe-up for consultation\",\n  \"instagram_caption\": \"Visual storytelling with engagement hooks and hashtags\",\n  \"instagram_story\": \"Quick value-add with poll/question sticker\",\n  \"instagram_reel_script\": \"Hook-Problem-Solution-Proof-CTA format\",\n  \"linkedin_post\": \"Professional B2B content targeting business owners\",\n  \"linkedin_article_title\": \"Thought leadership article headline\",\n  \"twitter_post\": \"Punchy, retweetable insight with trending potential\",\n  \"twitter_thread_opener\": \"Thread starter with promise of value\",\n  \"youtube_title\": \"Click-worthy title under 60 characters\",\n  \"youtube_description\": \"SEO-optimized description with timestamps\",\n  \"tiktok_script\": \"Viral-worthy script with trending elements\",\n  \"pinterest_title\": \"SEO-focused, keyword-rich pin title\",\n  \"pinterest_description\": \"Search-optimized description with value\",\n  \"telegram_message\": \"Direct, action-oriented message\",\n  \"email_subject\": \"High open-rate subject with curiosity gap\",\n  \"email_preview\": \"Compelling preview text\"\n}\n\nADVANCED PSYCHOLOGICAL TACTICS TO INCLUDE:\n1. SCARCITY: Limited-time offers, exclusive access\n2. SOCIAL PROOF: Client results, testimonials, case studies\n3. AUTHORITY: Industry expertise, thought leadership\n4. RECIPROCITY: Free value, helpful insights\n5. COMMITMENT: Interactive elements, polls, questions\n6. URGENCY: Time-sensitive opportunities\n7. LOSS AVERSION: What they're missing without automation\n8. CURIOSITY GAP: Incomplete information that drives action\n\nCONTENT REQUIREMENTS:\n• Each platform gets UNIQUE copy (no duplicates)\n• Include specific numbers and results (500% ROI, etc.)\n• Address pain points directly\n• Provide clear value propositions\n• Include strong, action-oriented CTAs\n• Use psychological triggers naturally\n• Maintain professional yet approachable tone\n• Include https://godigitalmarketing.com strategically\n• Focus on client acquisition and lead generation\n• Create FOMO and urgency where appropriate\n\nRETURN ONLY VALID JSON with all platform keys. Make each piece irresistible and conversion-focused."}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.6, "position": [-1800, 150], "id": "content-creator-001", "name": "AI Content Creator"}, {"parameters": {"jsCode": "// Advanced Content Processor with Platform-Specific Optimization\nconst aiResponse = $input.first()?.json?.response || $input.first()?.json?.output || '';\nconst config = $('Ultimate AI Configuration').item.json;\nconst strategy = config.todays_strategy;\n\nlet platformContent = {};\n\n// Extract AI-generated content\ntry {\n  const jsonMatch = aiResponse.match(/\\{[\\s\\S]*\\}/);\n  if (jsonMatch) {\n    platformContent = JSON.parse(jsonMatch[0]);\n  }\n} catch (error) {\n  console.log('Using advanced fallback content generation');\n}\n\n// Advanced Fallback with Psychological Triggers\nif (Object.keys(platformContent).length === 0) {\n  const psychologyTriggers = {\n    scarcity: ['Limited spots available', 'Only 5 consultations left this week', 'Exclusive offer ending soon'],\n    social_proof: ['500+ businesses transformed', 'Join 1000+ successful clients', 'Trusted by industry leaders'],\n    authority: ['Industry-leading expertise', 'Proven track record', 'Award-winning strategies'],\n    urgency: ['Act now', 'Don\\'t wait', 'Time-sensitive opportunity'],\n    curiosity: ['The secret that changed everything', 'What 90% of businesses don\\'t know', 'Hidden strategy revealed']\n  };\n  \n  const hooks = [\n    `🚀 ${strategy.content_pillars[0]}: The game-changing approach that's transforming businesses`,\n    `💡 Discover the ${strategy.type} strategy generating 500%+ ROI for our clients`,\n    `🔥 Why 90% of businesses fail at ${strategy.type} (and how to be the 10% that succeeds)`,\n    `⚡ The ${strategy.type} secret that generated $1M+ for our agency clients`\n  ];\n  \n  const selectedHook = hooks[Math.floor(Math.random() * hooks.length)];\n  const scarcityTrigger = psychologyTriggers.scarcity[Math.floor(Math.random() * psychologyTriggers.scarcity.length)];\n  const socialProof = psychologyTriggers.social_proof[Math.floor(Math.random() * psychologyTriggers.social_proof.length)];\n  const urgencyTrigger = psychologyTriggers.urgency[Math.floor(Math.random() * psychologyTriggers.urgency.length)];\n  \n  platformContent = {\n    facebook_post: `${selectedHook}\\n\\n🎯 Here's what we've learned from transforming ${socialProof.split(' ')[0]} businesses:\\n\\n✅ Automated systems generate 5x more leads\\n✅ AI-powered campaigns reduce costs by 60%\\n✅ Smart workflows save 20+ hours weekly\\n\\n💬 What's your biggest marketing challenge? Comment below!\\n\\n🔗 Free Strategy Session: https://godigitalmarketing.com\\n\\n${strategy.hashtags}`,\n    \n    facebook_story: `${selectedHook}\\n\\n🔥 ${scarcityTrigger}\\n\\nSwipe up for your free consultation! 👆\\n\\n#GODDigitalMarketing`,\n    \n    instagram_caption: `${selectedHook} ✨\\n\\n🎯 Real talk: Most businesses are stuck in manual marketing hell...\\n\\nBut our clients? They're crushing it with:\\n• 500%+ ROI from AI automation\\n• 60% lower acquisition costs\\n• 20+ hours saved weekly\\n\\n💭 Save this post if you're ready to scale!\\n\\n👥 Tag someone who needs this transformation\\n\\n🔗 Link in bio for free strategy session\\n\\n${strategy.hashtags}`,\n    \n    instagram_story: `${selectedHook}\\n\\n🔥 ${scarcityTrigger}\\n\\nTap the link in bio! 👆`,\n    \n    instagram_reel_script: `Hook: ${selectedHook}\\nProblem: Manual marketing is killing your growth\\nSolution: AI-powered automation systems\\nProof: 500%+ ROI for 1000+ clients\\nCTA: Link in bio for free consultation`,\n    \n    linkedin_post: `${selectedHook}\\n\\n🎯 After working with 1000+ businesses, here's the harsh truth:\\n\\n90% are wasting money on outdated marketing tactics.\\n\\nThe 10% that succeed? They've embraced AI automation.\\n\\n📊 Our clients see:\\n• 500%+ ROI increase\\n• 60% cost reduction\\n• 20+ hours saved weekly\\n\\n💡 Ready to join the winning 10%?\\n\\n${urgencyTrigger} - Free strategy session below.\\n\\n🔗 https://godigitalmarketing.com\\n\\n#DigitalMarketing #AIAutomation #BusinessGrowth`,\n    \n    linkedin_article_title: `${selectedHook.replace(/🚀|💡|🔥|⚡/, '').trim()} | GOD Digital Marketing`,\n    \n    twitter_post: `${selectedHook}\\n\\n${socialProof} can't be wrong.\\n\\n${urgencyTrigger}.\\n\\n🔗 https://godigitalmarketing.com\\n\\n${strategy.hashtags}`,\n    \n    twitter_thread_opener: `🧵 THREAD: ${selectedHook} (1/8)\\n\\nAfter analyzing 1000+ marketing campaigns, here's what separates winners from losers... 👇`,\n    \n    youtube_title: `${selectedHook.replace(/🚀|💡|🔥|⚡|✨/, '').substring(0, 55)} | GOD Digital`,\n    \n    youtube_description: `${selectedHook}\\n\\n🎯 In this video, discover:\\n• Why 90% of businesses fail at digital marketing\\n• The AI automation secrets generating 500%+ ROI\\n• Step-by-step implementation guide\\n• Real client case studies and results\\n\\n⏰ TIMESTAMPS:\\n0:00 Introduction\\n1:30 The Problem\\n3:00 Our Solution\\n5:30 Case Studies\\n7:00 Implementation\\n8:30 Free Resources\\n\\n🔗 RESOURCES:\\n• Free Strategy Session: https://godigitalmarketing.com\\n• Download Templates: https://godigitalmarketing.com/resources\\n\\n${strategy.hashtags}\\n\\n---\\nGOD Digital Marketing - Transforming Businesses Through AI-Powered Solutions`,\n    \n    tiktok_script: `Hook: ${selectedHook}\\nProblem: Your marketing is stuck in 2020\\nSolution: AI automation that actually works\\nProof: 500%+ ROI for our clients\\nCTA: Link in bio for free consultation\\nHashtags: #BusinessTips #MarketingHacks #AIAutomation #Entrepreneur`,\n    \n    pinterest_title: `${strategy.type.charAt(0).toUpperCase() + strategy.type.slice(1)} Strategy That Generated 500%+ ROI | GOD Digital Marketing`,\n    \n    pinterest_description: `${selectedHook}\\n\\n✅ Proven strategies that work\\n✅ Step-by-step implementation\\n✅ Real results from 1000+ clients\\n\\n${urgencyTrigger} - Get your free strategy session:\\n🔗 https://godigitalmarketing.com\\n\\n${strategy.hashtags}`,\n    \n    telegram_message: `🎯 ${selectedHook}\\n\\n💡 Quick question: Are you still doing marketing the hard way?\\n\\nOur clients automated their way to 500%+ ROI.\\n\\n${scarcityTrigger}\\n\\n🔗 Book your free consultation: https://godigitalmarketing.com`,\n    \n    email_subject: `${selectedHook.replace(/🚀|💡|🔥|⚡|✨/, '').substring(0, 45)}... [${scarcityTrigger.split(' ')[0]}]`,\n    \n    email_preview: `${socialProof}. Discover the automation secrets that changed everything. ${scarcityTrigger}.`\n  };\n}\n\n// Advanced Quality Assessment\nconst qualityMetrics = {\n  uniqueness_check: Object.values(platformContent).every((content, index, arr) => \n    arr.filter(c => c === content).length === 1\n  ),\n  psychology_integration: Object.values(platformContent).every(content => \n    content.includes('500%') || content.includes('ROI') || content.includes('transform') || \n    content.includes('automat') || content.includes('client') || content.includes('result')\n  ),\n  cta_presence: Object.values(platformContent).every(content => \n    content.includes('godigitalmarketing.com') || content.includes('consultation') || \n    content.includes('strategy') || content.includes('free')\n  ),\n  engagement_hooks: Object.values(platformContent).every(content => \n    content.includes('?') || content.includes('!') || content.includes('👇') || \n    content.includes('comment') || content.includes('share') || content.includes('tag')\n  ),\n  platform_optimization: Object.keys(platformContent).length >= 15\n};\n\nconst qualityScore = Object.values(qualityMetrics).filter(Boolean).length / Object.keys(qualityMetrics).length * 10;\n\nreturn {\n  ...platformContent,\n  quality_assessment: {\n    ...qualityMetrics,\n    overall_score: qualityScore,\n    content_ready: qualityScore >= 8,\n    platforms_covered: Object.keys(platformContent).length\n  },\n  strategy_info: {\n    theme: strategy.theme,\n    psychology: strategy.psychology,\n    focus: strategy.focus\n  },\n  processing_complete: true,\n  timestamp: new Date().toISOString()\n};"}, "id": "content-processor-001", "name": "Content Processor", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1600, 150]}, {"parameters": {"url": "https://api.unsplash.com/search/photos", "sendQuery": true, "queryParameters": {"parameters": [{"name": "query", "value": "={{ $('Ultimate AI Configuration').item.json.todays_strategy.theme.replace(/[^a-zA-Z0-9 ]/g, '') }} business marketing professional success"}, {"name": "per_page", "value": "10"}, {"name": "orientation", "value": "landscape"}, {"name": "content_filter", "value": "high"}, {"name": "order_by", "value": "relevant"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Client-ID 7l4GL0n2dGxLBvqIb9rroC8RfYNQgTuHWyLqsdcQsjA"}]}, "options": {"timeout": 15000}}, "id": "image-search-001", "name": "Primary Image Search", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [-1400, 150]}, {"parameters": {"jsCode": "// Advanced Image Processor with Platform-Specific Optimization\nconst imageData = $input.first()?.json;\nconst contentData = $('Content Processor').item.json;\nconst config = $('Ultimate AI Configuration').item.json;\n\nlet selectedImages = [];\nlet primaryImage = null;\n\n// Process Unsplash results\nif (imageData?.results && Array.isArray(imageData.results) && imageData.results.length > 0) {\n  selectedImages = imageData.results.slice(0, 5).map(img => ({\n    id: img.id,\n    url: img.urls.regular,\n    url_hd: img.urls.full,\n    url_thumb: img.urls.thumb,\n    alt: `${config.todays_strategy.theme} - GOD Digital Marketing`,\n    credit: `Photo by ${img.user.name} on Unsplash`,\n    width: img.width,\n    height: img.height\n  }));\n  primaryImage = selectedImages[0];\n}\n\n// Fallback images if Unsplash fails\nif (!primaryImage) {\n  const themeKeyword = config.todays_strategy.theme.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();\n  \n  const fallbackImages = [\n    {\n      id: 'unsplash_direct_1',\n      url: `https://source.unsplash.com/1200x630/?${themeKeyword},business,marketing,success`,\n      url_hd: `https://source.unsplash.com/1920x1080/?${themeKeyword},business,marketing,success`,\n      url_thumb: `https://source.unsplash.com/400x300/?${themeKeyword},business,marketing,success`,\n      alt: `${config.todays_strategy.theme} - GOD Digital Marketing`,\n      credit: 'Unsplash Source API',\n      width: 1200,\n      height: 630\n    },\n    {\n      id: 'branded_placeholder',\n      url: 'https://via.placeholder.com/1200x630/4F46E5/FFFFFF?text=GOD+Digital+Marketing',\n      url_hd: 'https://via.placeholder.com/1920x1080/4F46E5/FFFFFF?text=GOD+Digital+Marketing',\n      url_thumb: 'https://via.placeholder.com/400x300/4F46E5/FFFFFF?text=GOD+Digital+Marketing',\n      alt: `${config.todays_strategy.theme} - GOD Digital Marketing`,\n      credit: 'GOD Digital Marketing Brand Asset',\n      width: 1200,\n      height: 630\n    }\n  ];\n  \n  selectedImages = fallbackImages;\n  primaryImage = fallbackImages[0];\n}\n\n// Platform-specific image optimization\nconst platformImages = {\n  facebook_feed: primaryImage.url,\n  facebook_story: primaryImage.url.replace('1200x630', '1080x1920'),\n  instagram_feed: primaryImage.url.replace('1200x630', '1080x1080'),\n  instagram_story: primaryImage.url.replace('1200x630', '1080x1920'),\n  instagram_reel: primaryImage.url.replace('1200x630', '1080x1920'),\n  linkedin_post: primaryImage.url,\n  twitter_post: primaryImage.url.replace('1200x630', '1200x675'),\n  youtube_thumbnail: primaryImage.url.replace('1200x630', '1280x720'),\n  tiktok_video: primaryImage.url.replace('1200x630', '1080x1920'),\n  pinterest_pin: primaryImage.url.replace('1200x630', '1000x1500'),\n  telegram_image: primaryImage.url,\n  email_header: primaryImage.url.replace('1200x630', '600x300')\n};\n\nreturn {\n  primary_image: primaryImage,\n  platform_images: platformImages,\n  alternative_images: selectedImages.slice(1, 3),\n  image_ready: true,\n  quality_score: primaryImage.width >= 1200 ? 10 : 8,\n  timestamp: new Date().toISOString()\n};"}, "id": "image-processor-001", "name": "Image Processor", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1200, 150]}, {"parameters": {"jsCode": "// Enhanced Facebook Publisher with Image + Content\nconst imageUrl = $('Image Processor').item.json.platform_images.facebook_feed;\nconst content = $('Content Processor').item.json.facebook_post;\nconst accessToken = $credentials.facebookGraphApi.accessToken;\nconst pageId = 'YOUR_FACEBOOK_PAGE_ID'; // Replace with your actual page ID\n\ntry {\n  // Method 1: Post photo with caption\n  const response = await fetch(`https://graph.facebook.com/v18.0/${pageId}/photos`, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json'\n    },\n    body: JSON.stringify({\n      url: imageUrl,\n      caption: content,\n      link: 'https://godigitalmarketing.com',\n      published: true,\n      access_token: accessToken\n    })\n  });\n\n  const result = await response.json();\n\n  if (result.error) {\n    // Fallback: Post as link with image\n    const linkResponse = await fetch(`https://graph.facebook.com/v18.0/${pageId}/feed`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({\n        message: content,\n        link: 'https://godigitalmarketing.com',\n        picture: imageUrl,\n        access_token: accessToken\n      })\n    });\n    \n    const linkResult = await linkResponse.json();\n    \n    return {\n      success: !linkResult.error,\n      post_id: linkResult.id,\n      platform: 'facebook',\n      method: 'link_post',\n      content_length: content.length,\n      image_url: imageUrl,\n      error: linkResult.error || null,\n      timestamp: new Date().toISOString()\n    };\n  }\n\n  return {\n    success: true,\n    post_id: result.id,\n    platform: 'facebook',\n    method: 'photo_post',\n    content_length: content.length,\n    image_url: imageUrl,\n    timestamp: new Date().toISOString()\n  };\n} catch (error) {\n  return {\n    success: false,\n    error: error.message,\n    platform: 'facebook',\n    content_length: content.length,\n    image_url: imageUrl,\n    timestamp: new Date().toISOString()\n  };\n}"}, "id": "facebook-publisher-001", "name": "Facebook Publisher", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1000, 50]}, {"parameters": {"jsCode": "// Enhanced Instagram Publisher with Image + Caption\nconst imageUrl = $('Image Processor').item.json.platform_images.instagram_feed;\nconst caption = $('Content Processor').item.json.instagram_caption;\nconst accessToken = $credentials.facebookGraphApi.accessToken;\nconst instagramAccountId = 'YOUR_INSTAGRAM_BUSINESS_ACCOUNT_ID'; // Replace with your actual Instagram Business Account ID\n\ntry {\n  // Step 1: Create Instagram media container\n  const createMediaResponse = await fetch(`https://graph.facebook.com/v18.0/${instagramAccountId}/media`, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json'\n    },\n    body: JSON.stringify({\n      image_url: imageUrl,\n      caption: caption,\n      access_token: accessToken\n    })\n  });\n\n  const mediaData = await createMediaResponse.json();\n\n  if (mediaData.error || !mediaData.id) {\n    throw new Error(`Failed to create Instagram media container: ${mediaData.error?.message || 'Unknown error'}`);\n  }\n\n  // Wait for media processing (Instagram requirement)\n  await new Promise(resolve => setTimeout(resolve, 3000));\n\n  // Step 2: Publish the media\n  const publishResponse = await fetch(`https://graph.facebook.com/v18.0/${instagramAccountId}/media_publish`, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json'\n    },\n    body: JSON.stringify({\n      creation_id: mediaData.id,\n      access_token: accessToken\n    })\n  });\n\n  const publishData = await publishResponse.json();\n\n  if (publishData.error) {\n    throw new Error(`Failed to publish Instagram media: ${publishData.error.message}`);\n  }\n\n  return {\n    success: true,\n    media_container_id: mediaData.id,\n    published_post_id: publishData.id,\n    platform: 'instagram',\n    content_type: 'image_with_caption',\n    content_length: caption.length,\n    image_url: imageUrl,\n    hashtags_count: (caption.match(/#/g) || []).length,\n    timestamp: new Date().toISOString()\n  };\n} catch (error) {\n  return {\n    success: false,\n    error: error.message,\n    platform: 'instagram',\n    content_length: caption.length,\n    image_url: imageUrl,\n    timestamp: new Date().toISOString()\n  };\n}"}, "id": "instagram-publisher-001", "name": "Instagram Publisher", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1000, 150]}, {"parameters": {"jsCode": "// Enhanced LinkedIn Publisher with Image + Content\nconst imageUrl = $('Image Processor').item.json.platform_images.linkedin_post;\nconst content = $('Content Processor').item.json.linkedin_post;\nconst accessToken = $credentials.linkedInOAuth2Api.accessToken;\nconst personId = 'YOUR_LINKEDIN_PERSON_ID'; // Replace with your actual LinkedIn Person ID\n\ntry {\n  // Step 1: Upload image to LinkedIn\n  const uploadResponse = await fetch('https://api.linkedin.com/v2/assets?action=registerUpload', {\n    method: 'POST',\n    headers: {\n      'Authorization': `Bearer ${accessToken}`,\n      'Content-Type': 'application/json'\n    },\n    body: JSON.stringify({\n      registerUploadRequest: {\n        recipes: ['urn:li:digitalmediaRecipe:feedshare-image'],\n        owner: `urn:li:person:${personId}`,\n        serviceRelationships: [{\n          relationshipType: 'OWNER',\n          identifier: 'urn:li:userGeneratedContent'\n        }]\n      }\n    })\n  });\n\n  const uploadData = await uploadResponse.json();\n  \n  if (uploadData.error) {\n    throw new Error(`LinkedIn upload registration failed: ${uploadData.error.message}`);\n  }\n\n  const uploadUrl = uploadData.value.uploadMechanism['com.linkedin.digitalmedia.uploading.MediaUploadHttpRequest'].uploadUrl;\n  const asset = uploadData.value.asset;\n\n  // Step 2: Upload the actual image\n  const imageResponse = await fetch(imageUrl);\n  const imageBuffer = await imageResponse.arrayBuffer();\n\n  await fetch(uploadUrl, {\n    method: 'POST',\n    headers: {\n      'Authorization': `Bearer ${accessToken}`\n    },\n    body: imageBuffer\n  });\n\n  // Step 3: Create the post with image\n  const postResponse = await fetch('https://api.linkedin.com/v2/ugcPosts', {\n    method: 'POST',\n    headers: {\n      'Authorization': `Bearer ${accessToken}`,\n      'Content-Type': 'application/json',\n      'X-Restli-Protocol-Version': '2.0.0'\n    },\n    body: JSON.stringify({\n      author: `urn:li:person:${personId}`,\n      lifecycleState: 'PUBLISHED',\n      specificContent: {\n        'com.linkedin.ugc.ShareContent': {\n          shareCommentary: {\n            text: content\n          },\n          shareMediaCategory: 'IMAGE',\n          media: [{\n            status: 'READY',\n            description: {\n              text: 'GOD Digital Marketing - Professional Content'\n            },\n            media: asset,\n            title: {\n              text: 'Digital Marketing Excellence'\n            }\n          }]\n        }\n      },\n      visibility: {\n        'com.linkedin.ugc.MemberNetworkVisibility': 'PUBLIC'\n      }\n    })\n  });\n\n  const postData = await postResponse.json();\n\n  if (postData.error) {\n    // Fallback: Post without image\n    const textPostResponse = await fetch('https://api.linkedin.com/v2/ugcPosts', {\n      method: 'POST',\n      headers: {\n        'Authorization': `Bearer ${accessToken}`,\n        'Content-Type': 'application/json',\n        'X-Restli-Protocol-Version': '2.0.0'\n      },\n      body: JSON.stringify({\n        author: `urn:li:person:${personId}`,\n        lifecycleState: 'PUBLISHED',\n        specificContent: {\n          'com.linkedin.ugc.ShareContent': {\n            shareCommentary: {\n              text: `${content}\\n\\n🔗 https://godigitalmarketing.com`\n            },\n            shareMediaCategory: 'NONE'\n          }\n        },\n        visibility: {\n          'com.linkedin.ugc.MemberNetworkVisibility': 'PUBLIC'\n        }\n      })\n    });\n\n    const textPostData = await textPostResponse.json();\n    \n    return {\n      success: !textPostData.error,\n      post_id: textPostData.id,\n      platform: 'linkedin',\n      method: 'text_post',\n      content_length: content.length,\n      image_url: imageUrl,\n      error: textPostData.error || null,\n      timestamp: new Date().toISOString()\n    };\n  }\n\n  return {\n    success: true,\n    post_id: postData.id,\n    platform: 'linkedin',\n    method: 'image_post',\n    content_length: content.length,\n    image_url: imageUrl,\n    asset_id: asset,\n    timestamp: new Date().toISOString()\n  };\n} catch (error) {\n  return {\n    success: false,\n    error: error.message,\n    platform: 'linkedin',\n    content_length: content.length,\n    image_url: imageUrl,\n    timestamp: new Date().toISOString()\n  };\n}"}, "id": "linkedin-publisher-001", "name": "LinkedIn Publisher", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1000, 250]}, {"parameters": {"jsCode": "// Enhanced Twitter Publisher with Image + Content\nconst imageUrl = $('Image Processor').item.json.platform_images.twitter_post;\nconst content = $('Content Processor').item.json.twitter_post;\nconst bearerToken = $credentials.twitterOAuth2Api.bearerToken;\n\ntry {\n  // Step 1: Upload media to Twitter\n  const imageResponse = await fetch(imageUrl);\n  const imageBuffer = await imageResponse.arrayBuffer();\n  const base64Image = Buffer.from(imageBuffer).toString('base64');\n\n  const mediaUploadResponse = await fetch('https://upload.twitter.com/1.1/media/upload.json', {\n    method: 'POST',\n    headers: {\n      'Authorization': `Bearer ${bearerToken}`,\n      'Content-Type': 'application/x-www-form-urlencoded'\n    },\n    body: `media_data=${encodeURIComponent(base64Image)}`\n  });\n\n  const mediaData = await mediaUploadResponse.json();\n\n  if (mediaData.errors) {\n    throw new Error(`Twitter media upload failed: ${mediaData.errors[0].message}`);\n  }\n\n  // Step 2: Create tweet with media\n  const tweetResponse = await fetch('https://api.twitter.com/2/tweets', {\n    method: 'POST',\n    headers: {\n      'Authorization': `Bearer ${bearerToken}`,\n      'Content-Type': 'application/json'\n    },\n    body: JSON.stringify({\n      text: content,\n      media: {\n        media_ids: [mediaData.media_id_string]\n      }\n    })\n  });\n\n  const tweetData = await tweetResponse.json();\n\n  if (tweetData.errors) {\n    // Fallback: Post without image\n    const textTweetResponse = await fetch('https://api.twitter.com/2/tweets', {\n      method: 'POST',\n      headers: {\n        'Authorization': `Bearer ${bearerToken}`,\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({\n        text: `${content}\\n\\n🔗 https://godigitalmarketing.com`\n      })\n    });\n\n    const textTweetData = await textTweetResponse.json();\n    \n    return {\n      success: !textTweetData.errors,\n      post_id: textTweetData.data?.id,\n      platform: 'twitter',\n      method: 'text_tweet',\n      content_length: content.length,\n      image_url: imageUrl,\n      error: textTweetData.errors?.[0]?.detail || null,\n      timestamp: new Date().toISOString()\n    };\n  }\n\n  return {\n    success: true,\n    post_id: tweetData.data.id,\n    platform: 'twitter',\n    method: 'image_tweet',\n    content_length: content.length,\n    image_url: imageUrl,\n    media_id: mediaData.media_id_string,\n    timestamp: new Date().toISOString()\n  };\n} catch (error) {\n  return {\n    success: false,\n    error: error.message,\n    platform: 'twitter',\n    content_length: content.length,\n    image_url: imageUrl,\n    timestamp: new Date().toISOString()\n  };\n}"}, "id": "twitter-publisher-001", "name": "Twitter Publisher", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1000, 350]}, {"parameters": {"jsCode": "// Enhanced Pinterest Publisher with Image + Content\nconst imageUrl = $('Image Processor').item.json.platform_images.pinterest_pin;\nconst title = $('Content Processor').item.json.pinterest_title;\nconst description = $('Content Processor').item.json.pinterest_description;\nconst accessToken = $credentials.pinterestOAuth2Api.accessToken;\nconst boardId = 'YOUR_PINTEREST_BOARD_ID'; // Replace with your actual Pinterest Board ID\n\ntry {\n  // Create Pinterest pin with image and content\n  const pinResponse = await fetch('https://api.pinterest.com/v5/pins', {\n    method: 'POST',\n    headers: {\n      'Authorization': `Bearer ${accessToken}`,\n      'Content-Type': 'application/json'\n    },\n    body: JSON.stringify({\n      board_id: boardId,\n      media_source: {\n        source_type: 'image_url',\n        url: imageUrl\n      },\n      title: title,\n      description: description,\n      link: 'https://godigitalmarketing.com',\n      note: 'GOD Digital Marketing - Professional Content'\n    })\n  });\n\n  const pinData = await pinResponse.json();\n\n  if (pinData.error) {\n    throw new Error(`Pinterest pin creation failed: ${pinData.error.message}`);\n  }\n\n  return {\n    success: true,\n    post_id: pinData.id,\n    platform: 'pinterest',\n    method: 'image_pin',\n    title_length: title.length,\n    description_length: description.length,\n    image_url: imageUrl,\n    pin_url: pinData.url,\n    board_id: boardId,\n    timestamp: new Date().toISOString()\n  };\n} catch (error) {\n  return {\n    success: false,\n    error: error.message,\n    platform: 'pinterest',\n    title_length: title.length,\n    description_length: description.length,\n    image_url: imageUrl,\n    timestamp: new Date().toISOString()\n  };\n}"}, "id": "pinterest-publisher-001", "name": "Pinterest Publisher", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1000, 450]}, {"parameters": {"jsCode": "// Enhanced Telegram Publisher with Image + Content\nconst imageUrl = $('Image Processor').item.json.platform_images.telegram_image;\nconst message = $('Content Processor').item.json.telegram_message;\nconst botToken = $credentials.telegramApi.token;\nconst chatId = 'YOUR_TELEGRAM_CHANNEL_ID'; // Replace with your actual Telegram Channel ID (e.g., @yourchannel)\n\ntry {\n  // Send photo with caption to Telegram\n  const telegramResponse = await fetch(`https://api.telegram.org/bot${botToken}/sendPhoto`, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json'\n    },\n    body: JSON.stringify({\n      chat_id: chatId,\n      photo: imageUrl,\n      caption: message,\n      parse_mode: 'HTML',\n      disable_web_page_preview: false\n    })\n  });\n\n  const telegramData = await telegramResponse.json();\n\n  if (!telegramData.ok) {\n    // Fallback: Send as text message with link to image\n    const textResponse = await fetch(`https://api.telegram.org/bot${botToken}/sendMessage`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({\n        chat_id: chatId,\n        text: `${message}\\n\\n📸 Image: ${imageUrl}\\n\\n🔗 https://godigitalmarketing.com`,\n        parse_mode: 'HTML',\n        disable_web_page_preview: false\n      })\n    });\n\n    const textData = await textResponse.json();\n    \n    return {\n      success: textData.ok,\n      post_id: textData.result?.message_id,\n      platform: 'telegram',\n      method: 'text_message',\n      content_length: message.length,\n      image_url: imageUrl,\n      chat_id: chatId,\n      error: textData.description || null,\n      timestamp: new Date().toISOString()\n    };\n  }\n\n  return {\n    success: true,\n    post_id: telegramData.result.message_id,\n    platform: 'telegram',\n    method: 'photo_message',\n    content_length: message.length,\n    image_url: imageUrl,\n    chat_id: chatId,\n    timestamp: new Date().toISOString()\n  };\n} catch (error) {\n  return {\n    success: false,\n    error: error.message,\n    platform: 'telegram',\n    content_length: message.length,\n    image_url: imageUrl,\n    timestamp: new Date().toISOString()\n  };\n}"}, "id": "telegram-publisher-001", "name": "Telegram Publisher", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1000, 550]}, {"parameters": {"jsCode": "// Ultimate Analytics & Lead Tracking Engine\nconst config = $('Ultimate AI Configuration').item.json;\nconst contentData = $('Content Processor').item.json;\nconst imageData = $('Image Processor').item.json;\n\n// Collect publishing results from all platforms\nlet publishingResults = {\n  facebook: { attempted: true, success: false, error: null, post_id: null },\n  instagram: { attempted: true, success: false, error: null, post_id: null },\n  linkedin: { attempted: true, success: false, error: null, post_id: null },\n  twitter: { attempted: true, success: false, error: null, post_id: null },\n  pinterest: { attempted: true, success: false, error: null, post_id: null },\n  telegram: { attempted: true, success: false, error: null, post_id: null }\n};\n\n// Try to collect results from each publisher\ntry {\n  const facebookResult = $('Facebook Publisher').item?.json;\n  if (facebookResult && facebookResult.id) {\n    publishingResults.facebook = { attempted: true, success: true, post_id: facebookResult.id, error: null };\n  }\n} catch (error) {\n  publishingResults.facebook.error = error.message;\n}\n\ntry {\n  const instagramResult = $('Instagram Publisher').item?.json;\n  if (instagramResult && instagramResult.success) {\n    publishingResults.instagram = { attempted: true, success: true, post_id: instagramResult.published_post_id, error: null };\n  }\n} catch (error) {\n  publishingResults.instagram.error = error.message;\n}\n\ntry {\n  const linkedinResult = $('LinkedIn Publisher').item?.json;\n  if (linkedinResult && linkedinResult.id) {\n    publishingResults.linkedin = { attempted: true, success: true, post_id: linkedinResult.id, error: null };\n  }\n} catch (error) {\n  publishingResults.linkedin.error = error.message;\n}\n\ntry {\n  const twitterResult = $('Twitter Publisher').item?.json;\n  if (twitterResult && twitterResult.data) {\n    publishingResults.twitter = { attempted: true, success: true, post_id: twitterResult.data.id, error: null };\n  }\n} catch (error) {\n  publishingResults.twitter.error = error.message;\n}\n\ntry {\n  const pinterestResult = $('Pinterest Publisher').item?.json;\n  if (pinterestResult && pinterestResult.id) {\n    publishingResults.pinterest = { attempted: true, success: true, post_id: pinterestResult.id, error: null };\n  }\n} catch (error) {\n  publishingResults.pinterest.error = error.message;\n}\n\ntry {\n  const telegramResult = $('Telegram Publisher').item?.json;\n  if (telegramResult && telegramResult.result) {\n    publishingResults.telegram = { attempted: true, success: true, post_id: telegramResult.result.message_id, error: null };\n  }\n} catch (error) {\n  publishingResults.telegram.error = error.message;\n}\n\n// Calculate success metrics\nconst successfulPosts = Object.values(publishingResults).filter(result => result.success).length;\nconst totalAttempts = Object.keys(publishingResults).length;\nconst successRate = (successfulPosts / totalAttempts) * 100;\n\n// Lead generation predictions based on content psychology\nconst leadPredictions = {\n  estimated_reach: successfulPosts * 2500, // Conservative estimate per platform\n  expected_engagement_rate: contentData.strategy_info.psychology.includes('Social Proof') ? 8.5 : 6.2,\n  predicted_leads: Math.round(successfulPosts * 3.5), // Based on conversion psychology\n  lead_quality_score: contentData.strategy_info.psychology.includes('Authority') ? 9 : 7,\n  conversion_probability: contentData.strategy_info.focus.includes('call-to-action') ? 0.15 : 0.08,\n  revenue_potential: Math.round(successfulPosts * 3.5 * 750) // $750 average client value\n};\n\n// Advanced psychological impact assessment\nconst psychologyImpact = {\n  scarcity_triggers: Object.values(contentData).filter(content => \n    content.includes('limited') || content.includes('exclusive') || content.includes('only')\n  ).length,\n  social_proof_elements: Object.values(contentData).filter(content => \n    content.includes('500%') || content.includes('1000+') || content.includes('clients')\n  ).length,\n  urgency_creation: Object.values(contentData).filter(content => \n    content.includes('now') || content.includes('today') || content.includes('act')\n  ).length,\n  authority_building: Object.values(contentData).filter(content => \n    content.includes('expert') || content.includes('proven') || content.includes('industry')\n  ).length,\n  curiosity_gaps: Object.values(contentData).filter(content => \n    content.includes('secret') || content.includes('discover') || content.includes('reveal')\n  ).length\n};\n\n// Client acquisition tracking\nconst clientAcquisition = {\n  content_funnel_stage: config.lead_generation.content_funnel,\n  conversion_optimization: {\n    pain_point_addressed: contentData.strategy_info.focus.includes('challenge') || contentData.strategy_info.focus.includes('problem'),\n    solution_presented: Object.values(contentData).some(content => content.includes('solution') || content.includes('automat')),\n    social_proof_included: psychologyImpact.social_proof_elements > 0,\n    clear_cta_present: Object.values(contentData).every(content => content.includes('godigitalmarketing.com')),\n    urgency_created: psychologyImpact.urgency_creation > 0\n  },\n  lead_magnets_offered: [\n    'Free Strategy Session',\n    'Marketing Automation Guide',\n    'ROI Calculator Tool',\n    'Case Study Collection'\n  ],\n  expected_client_value: 2500, // Average client lifetime value\n  acquisition_cost: 0.05 // Extremely low due to automation\n};\n\nreturn {\n  execution_summary: {\n    timestamp: new Date().toISOString(),\n    content_theme: contentData.strategy_info.theme,\n    psychology_framework: contentData.strategy_info.psychology,\n    content_quality: contentData.quality_assessment.overall_score,\n    image_quality: imageData.quality_score,\n    system_performance: 'optimal'\n  },\n  publishing_results: publishingResults,\n  success_metrics: {\n    successful_posts: successfulPosts,\n    total_attempts: totalAttempts,\n    success_rate: Math.round(successRate),\n    platforms_reached: successfulPosts,\n    content_uniqueness: contentData.quality_assessment.uniqueness_check\n  },\n  lead_predictions: leadPredictions,\n  psychology_impact: psychologyImpact,\n  client_acquisition: clientAcquisition,\n  roi_analysis: {\n    estimated_cost: 0.03, // Ultra-low automation cost\n    predicted_revenue: leadPredictions.revenue_potential,\n    roi_percentage: leadPredictions.revenue_potential > 0 ? Math.round((leadPredictions.revenue_potential - 0.03) / 0.03 * 100) : 0,\n    payback_period: '1-3 days',\n    annual_value: leadPredictions.revenue_potential * 365\n  },\n  next_optimization: {\n    recommended_focus: successRate < 80 ? 'technical_optimization' : 'content_enhancement',\n    psychology_adjustment: psychologyImpact.scarcity_triggers < 2 ? 'increase_scarcity' : 'maintain_balance',\n    platform_priority: Object.entries(publishingResults)\n      .filter(([platform, result]) => result.success)\n      .map(([platform]) => platform),\n    content_iteration: 'continue_7_day_rotation'\n  },\n  system_health: {\n    automation_reliability: successRate >= 70 ? 'excellent' : 'good',\n    content_quality: 'premium',\n    lead_generation_active: true,\n    client_acquisition_optimized: true,\n    cost_efficiency: 'maximum'\n  }\n};"}, "id": "analytics-engine-001", "name": "Analytics & Lead Tracking", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-800, 300]}], "connections": {"Daily Content Scheduler": {"main": [[{"node": "Ultimate AI Configuration", "type": "main", "index": 0}]]}, "Manual Test Trigger": {"main": [[{"node": "Ultimate AI Configuration", "type": "main", "index": 0}]]}, "Ultimate AI Configuration": {"main": [[{"node": "Groq AI Content Generator", "type": "main", "index": 0}]]}, "Groq AI Content Generator": {"main": [[{"node": "AI Content Creator", "type": "main", "index": 0}]]}, "AI Content Creator": {"main": [[{"node": "Content Processor", "type": "main", "index": 0}]]}, "Content Processor": {"main": [[{"node": "Primary Image Search", "type": "main", "index": 0}]]}, "Primary Image Search": {"main": [[{"node": "Image Processor", "type": "main", "index": 0}]]}, "Image Processor": {"main": [[{"node": "Facebook Publisher", "type": "main", "index": 0}, {"node": "Instagram Publisher", "type": "main", "index": 0}, {"node": "LinkedIn Publisher", "type": "main", "index": 0}, {"node": "Twitter Publisher", "type": "main", "index": 0}, {"node": "Pinterest Publisher", "type": "main", "index": 0}, {"node": "Telegram Publisher", "type": "main", "index": 0}]]}, "Facebook Publisher": {"main": [[{"node": "Analytics & Lead Tracking", "type": "main", "index": 0}]]}, "Instagram Publisher": {"main": [[{"node": "Analytics & Lead Tracking", "type": "main", "index": 0}]]}, "LinkedIn Publisher": {"main": [[{"node": "Analytics & Lead Tracking", "type": "main", "index": 0}]]}, "Twitter Publisher": {"main": [[{"node": "Analytics & Lead Tracking", "type": "main", "index": 0}]]}, "Pinterest Publisher": {"main": [[{"node": "Analytics & Lead Tracking", "type": "main", "index": 0}]]}, "Telegram Publisher": {"main": [[{"node": "Analytics & Lead Tracking", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "ultimate-v4.0", "meta": {"templateCredsSetupCompleted": true}, "id": "god-digital-ultimate-automation", "tags": [{"createdAt": "2025-01-20T00:00:00.000Z", "updatedAt": "2025-01-20T00:00:00.000Z", "id": "ultimate-automation", "name": "Ultimate Social Media Automation"}]}